import aiohttp
import asyncio
import time
from collections import defaultdict
import csv
import os
import json
from urllib.parse import urlparse
from datetime import datetime
from pathlib import Path
import hashlib

# 示例：10万个下载链接（这里用占位符代替，实际使用时替换成你的链接列表）
# 注意：这里只是示例，实际中你应该从文件或数据库读取真实的10w个链接
# 示例链接格式：[" http://example.com/image1.jpg ", " http://example.com/image2.jpg ", ...]
# 为了演示，我们生成10万个假的URL（实际使用时请替换成真实链接）
# def generate_sample_links(num=100000):
#     base_url = "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/png_m_b7548e183b9dc6e0edd344531525a2ae_sx_520138_www720-720"
#     return [f"{base_url}{i}.jpg" for i in range(num)]

# 获取脚本所在目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
LINKS = os.path.join(SCRIPT_DIR, "links.txt")

# 实际使用时，你可以从文件读取链接，例如：
if os.path.exists(LINKS):
    with open(LINKS, 'r') as f:
        links = [line.strip() for line in f if line.strip()]
else:
    print(f"警告：链接文件 {LINKS} 不存在")
    print("请创建 links.txt 文件或修改 LINKS 变量")
    # 使用示例链接进行测试
    links = [
        "https://httpbin.org/image/jpeg",
        "https://httpbin.org/image/png",
        "https://httpbin.org/image/webp"
    ]
    print(f"使用示例链接进行测试，共 {len(links)} 个链接")

# 这里使用示例链接
# links = generate_sample_links(100000)

# 统计结果
stats = defaultdict(int)
results = []

# 配置参数
DOWNLOAD_DIR = "/home/<USER>/download"  # 图片保存目录
OUTPUT_DIR = "/home/<USER>/output"  # 文件输出目录
QPS = 20  # 控制 QPS（每秒请求数）
MAX_CONCURRENT = 100  # 最大并发数
semaphore = asyncio.Semaphore(MAX_CONCURRENT)

# QPS控制相关变量
request_times = []  # 记录请求时间
qps_lock = asyncio.Lock()  # QPS控制锁

# 确保下载目录存在
os.makedirs(DOWNLOAD_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

def setup_output_directories():
    """设置输出目录结构"""
    directories = [
        Path(OUTPUT_DIR),
        Path(OUTPUT_DIR) / "csv",
        Path(OUTPUT_DIR) / "json",
        Path(OUTPUT_DIR) / "logs",
        Path(OUTPUT_DIR) / "reports"
    ]

    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def save_results_to_files(results, stats):
    """保存结果到多种格式的文件"""
    setup_output_directories()

    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 为每个结果添加时间戳
    for result in results:
        if 'timestamp' not in result:
            result['timestamp'] = datetime.now().isoformat()
        # 确保所有字段都存在
        for field in ['filepath', 'filesize', 'error']:
            if field not in result:
                result[field] = '' if field != 'filesize' else 0

    # 保存CSV文件
    csv_file = Path(OUTPUT_DIR) / "csv" / f"download_results_{timestamp}.csv"
    csv_latest = Path(OUTPUT_DIR) / "csv" / "download_results_latest.csv"

    fieldnames = ['url', 'status', 'success', 'filepath', 'filesize', 'error', 'timestamp']

    # 写入带时间戳的CSV文件
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    # 写入最新CSV文件
    with open(csv_latest, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    # 保存JSON文件
    json_file = Path(OUTPUT_DIR) / "json" / f"download_results_{timestamp}.json"
    json_latest = Path(OUTPUT_DIR) / "json" / "download_results_latest.json"

    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    with open(json_latest, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    # 生成汇总报告
    total_success = sum(1 for r in results if r.get('success', False))
    total_failed = len(results) - total_success
    total_size = sum(r.get('filesize', 0) for r in results if r.get('success', False))

    report_file = Path(OUTPUT_DIR) / "reports" / f"summary_report_{timestamp}.txt"

    report_content = f"""下载任务汇总报告
================
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

基本统计:
---------
总链接数: {len(results)}
下载成功: {total_success}
下载失败: {total_failed}
成功率: {(total_success/len(results)*100):.2f}%
总文件大小: {total_size/(1024*1024):.2f} MB

状态码统计:
-----------
"""

    for status, count in stats.items():
        report_content += f"状态码 {status}: {count} 次\n"

    report_content += f"""
文件输出位置:
-------------
CSV文件: {csv_file}
JSON文件: {json_file}
报告文件: {report_file}
"""

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)

    print(f"\n=== 结果已保存 ===")
    print(f"CSV文件: {csv_file}")
    print(f"JSON文件: {json_file}")
    print(f"报告文件: {report_file}")
    print(f"最新文件: {csv_latest}")

    return {
        'csv_file': csv_file,
        'json_file': json_file,
        'report_file': report_file,
        'csv_latest': csv_latest,
        'json_latest': json_latest
    }

async def wait_for_qps():
    """QPS控制函数 - 确保不超过设定的QPS"""
    async with qps_lock:
        current_time = time.time()

        # 清理1秒前的请求记录
        global request_times
        request_times = [t for t in request_times if current_time - t < 1.0]

        # 如果当前秒内的请求数已达到QPS限制，则等待
        if len(request_times) >= QPS:
            sleep_time = 1.0 - (current_time - request_times[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                # 重新清理过期的请求记录
                current_time = time.time()
                request_times = [t for t in request_times if current_time - t < 1.0]

        # 记录当前请求时间
        request_times.append(current_time)

def generate_filename(url, index):
    """生成文件名"""
    try:
        # 尝试从URL中提取文件扩展名
        parsed_url = urlparse(url)
        path = parsed_url.path
        if path and '.' in path:
            ext = os.path.splitext(path)[1]
            if ext.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                return f"image_{index:06d}{ext}"

        # 如果无法从URL获取扩展名，使用URL的hash值
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        return f"image_{index:06d}_{url_hash}.jpg"
    except:
        return f"image_{index:06d}.jpg"

async def download_image(session, url, index):
    # QPS控制 - 在发起请求前等待
    await wait_for_qps()

    async with semaphore:
        start_time = time.time()
        filename = generate_filename(url, index)
        filepath = os.path.join(DOWNLOAD_DIR, filename)

        try:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                status = response.status
                stats[status] += 1

                if status == 200:
                    # 读取图片内容
                    content = await response.read()

                    # 保存图片文件
                    with open(filepath, 'wb') as f:
                        f.write(content)

                    result = {
                        'url': url,
                        'status': status,
                        'success': True,
                        'filepath': filepath,
                        'filesize': len(content)
                    }
                    elapsed_time = time.time() - start_time
                    print(f"✓ 下载成功: {filename} ({len(content)} bytes) - 耗时: {elapsed_time:.2f}s")
                else:
                    result = {
                        'url': url,
                        'status': status,
                        'success': False,
                        'filepath': '',
                        'filesize': 0
                    }
                    elapsed_time = time.time() - start_time
                    print(f"✗ 下载失败: {url}, Status: {status} - 耗时: {elapsed_time:.2f}s")

                results.append(result)

        except Exception as e:
            # 捕获所有异常，比如超时、连接错误等
            stats['error'] += 1
            result = {
                'url': url,
                'status': 'error',
                'success': False,
                'filepath': '',
                'filesize': 0,
                'error': str(e)
            }
            results.append(result)
            elapsed_time = time.time() - start_time
            print(f"✗ 下载异常: {url}, Error: {str(e)} - 耗时: {elapsed_time:.2f}s")

async def main():
    print(f"开始下载 {len(links)} 个图片到目录: {DOWNLOAD_DIR}")
    print(f"QPS限制: {QPS} 请求/秒")
    print(f"最大并发数: {MAX_CONCURRENT}")
    print(f"预计完成时间: {len(links) / QPS / 60:.1f} 分钟")

    start_time = time.time()

    connector = aiohttp.TCPConnector(limit=0)  # 不限制连接数，由 semaphore 控制并发
    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = [download_image(session, url, index) for index, url in enumerate(links, 1)]
        # 使用asyncio.gather执行所有任务，QPS控制在download_image函数中实现
        await asyncio.gather(*tasks)

    total_time = time.time() - start_time

    # 统计结果输出
    print("\n" + "="*50)
    print("下载统计结果：")
    total_success = sum(1 for r in results if r['success'])
    total_failed = len(results) - total_success
    total_size = sum(r.get('filesize', 0) for r in results if r['success'])
    actual_qps = len(links) / total_time if total_time > 0 else 0

    print(f"总链接数: {len(links)}")
    print(f"下载成功: {total_success}")
    print(f"下载失败: {total_failed}")
    print(f"总文件大小: {total_size / (1024*1024):.2f} MB")
    print(f"总耗时: {total_time:.2f} 秒")
    print(f"实际QPS: {actual_qps:.2f} 请求/秒")
    print(f"目标QPS: {QPS} 请求/秒")

    print("\n状态码统计：")
    for status, count in stats.items():
        print(f"  状态码 {status}: {count} 次")

    # 保存结果到多种格式
    saved_files = save_results_to_files(results, stats)



if __name__ == '__main__':
    asyncio.run(main())
