#!/bin/bash

# 启动脚本 - 自动激活虚拟环境并运行程序

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查虚拟环境是否存在（支持多种常见名称）
VENV_DIR=""
for venv_name in "venv" ".venv" "myenv" "env"; do
    if [ -d "$venv_name" ]; then
        VENV_DIR="$venv_name"
        break
    fi
done

if [ -z "$VENV_DIR" ]; then
    echo "错误：虚拟环境不存在"
    echo "请先创建虚拟环境："
    echo "  python3 -m venv venv"
    echo "  source venv/bin/activate"
    echo "  pip install -r requirements.txt"
    exit 1
fi

echo "找到虚拟环境: $VENV_DIR"

# 激活虚拟环境
source "$VENV_DIR/bin/activate"

echo "=== 环境信息 ==="
echo "Python路径: $(which python)"
echo "当前目录: $(pwd)"
echo "虚拟环境: $VIRTUAL_ENV"

# 检查并安装依赖
if [ -f "requirements.txt" ]; then
    echo "检查依赖..."
    pip install -r requirements.txt
else
    # 检查aiohttp是否安装
    python -c "import aiohttp" 2>/dev/null || {
        echo "错误：aiohttp未安装，正在安装..."
        pip install aiohttp
    }
fi

# 检查必要文件
LINKS_FILE="${SCRIPT_DIR}/links.txt"
if [ ! -f "$LINKS_FILE" ]; then
    echo "警告：links.txt 文件不存在于 $LINKS_FILE"
    echo "请确保链接文件存在，或者修改 run.py 中的 LINKS 变量"
fi

echo ""
echo "=== 开始运行程序 ==="
python run.py
